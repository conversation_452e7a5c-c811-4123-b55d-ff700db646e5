package com.akesobio.report.autoacct.service;

import com.akesobio.report.autoacct.domain.AutoMaterialAcctPurchaseNotreturn;
import com.akesobio.report.autoacct.domain.excle.impot.ExcelImportPurchaseNotreturn;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;


/**
 * 采购未回Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
public interface IAutoMaterialAcctPurchaseNotreturnService extends IService<AutoMaterialAcctPurchaseNotreturn>
{

    public void refreshPurchaseNotreturn() throws Exception;

    public String importData(List<ExcelImportPurchaseNotreturn> userList, Boolean isUpdateSupport, String operName);


    /**
     * 查询采购未回
     * 
     * @param id 采购未回主键
     * @return 采购未回
     */
    public AutoMaterialAcctPurchaseNotreturn selectAutoMaterialAcctPurchaseNotreturnById(Long id);

    /**
     * 查询采购未回列表
     * 
     * @param autoMaterialAcctPurchaseNotreturn 采购未回
     * @return 采购未回集合
     */
    public List<AutoMaterialAcctPurchaseNotreturn> selectAutoMaterialAcctPurchaseNotreturnList(AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn);

    /**
     * 新增采购未回
     * 
     * @param autoMaterialAcctPurchaseNotreturn 采购未回
     * @return 结果
     */
    public int insertAutoMaterialAcctPurchaseNotreturn(AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn);

    /**
     * 修改采购未回
     * 
     * @param autoMaterialAcctPurchaseNotreturn 采购未回
     * @return 结果
     */
    public int updateAutoMaterialAcctPurchaseNotreturn(AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn);

    /**
     * 批量删除采购未回
     * 
     * @param ids 需要删除的采购未回主键集合
     * @return 结果
     */
    public int deleteAutoMaterialAcctPurchaseNotreturnByIds(Long[] ids);

    /**
     * 删除采购未回信息
     * 
     * @param id 采购未回主键
     * @return 结果
     */
    public int deleteAutoMaterialAcctPurchaseNotreturnById(Long id);
}
