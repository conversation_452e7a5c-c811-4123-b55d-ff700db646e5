package com.akesobio.report.autoacct.service;

import com.akesobio.report.autoacct.domain.BomMaterialRequire;
import com.akesobio.report.autoacct.domain.excle.impot.ExcelImportBomMaterialRequire;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;


/**
 * BOM-物料需求Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-22
 */
public interface IAutoMaterialAcctBomMaterialRequireService extends IService<BomMaterialRequire>
{
    public String importData(List<ExcelImportBomMaterialRequire> userList, Boolean isUpdateSupport, String operName);


    /**
     * 查询BOM-物料需求
     *
     * @param id BOM-物料需求主键
     * @return BOM-物料需求
     */
    public BomMaterialRequire selectAutoMaterialAcctBomMaterialRequireById(Long id);

    /**
     * 查询BOM-物料需求列表
     * 
     * @param bomMaterialRequire BOM-物料需求
     * @return BOM-物料需求集合
     */
    public List<BomMaterialRequire> selectAutoMaterialAcctBomMaterialRequireList(BomMaterialRequire bomMaterialRequire);

    /**
     * 新增BOM-物料需求
     * 
     * @param bomMaterialRequire BOM-物料需求
     * @return 结果
     */
    public int insertAutoMaterialAcctBomMaterialRequire(BomMaterialRequire bomMaterialRequire);

    /**
     * 修改BOM-物料需求
     * 
     * @param bomMaterialRequire BOM-物料需求
     * @return 结果
     */
    public int updateAutoMaterialAcctBomMaterialRequire(BomMaterialRequire bomMaterialRequire);

    /**
     * 批量删除BOM-物料需求
     * 
     * @param ids 需要删除的BOM-物料需求主键集合
     * @return 结果
     */
    public int deleteAutoMaterialAcctBomMaterialRequireByIds(Long[] ids);

    /**
     * 删除BOM-物料需求信息
     * 
     * @param id BOM-物料需求主键
     * @return 结果
     */
    public int deleteAutoMaterialAcctBomMaterialRequireById(Long id);
}
