package com.akesobio.report.autoacct.service.impl;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.common.utils.http.HttpUtils;
import com.akesobio.report.autoacct.domain.*;
import com.akesobio.report.autoacct.domain.excle.impot.ExcelImportPurchaseNotreturn;
import com.akesobio.report.autoacct.mapper.AutoMaterialAcctInventoryMapper;

import com.akesobio.report.autoacct.mapper.AutoMaterialAcctPurchaseNotreturnMapper;
import com.akesobio.report.autoacct.mapper.PurchaseNotreturnOcMapper;
import com.akesobio.report.autoacct.service.IAutoMaterialAcctPurchaseNotreturnService;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 采购未回Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
@Service
public class AutoMaterialAcctPurchaseNotreturnServiceImpl extends ServiceImpl<AutoMaterialAcctPurchaseNotreturnMapper, AutoMaterialAcctPurchaseNotreturn> implements IAutoMaterialAcctPurchaseNotreturnService
{
    @Resource
    private AutoMaterialAcctPurchaseNotreturnMapper autoMaterialAcctPurchaseNotreturnMapper;

    @Resource
    private AutoMaterialAcctInventoryMapper autoMaterialAcctInventoryMapper;



    @Resource
    private PurchaseNotreturnOcMapper purchaseNotreturnOcMapper;


    @Override
    public void refreshPurchaseNotreturn() throws Exception {
        //从sap 同步采购未回数据

        String str = HttpUtils.sendPostJson("http://10.10.2.33:9911/sap/api/ZFI_FM_OA_GET_001", new JSONObject().toString());
        //清空表
        // 创建一个Wrapper，设置条件为id不为空
        LambdaQueryWrapper<AutoMaterialAcctPurchaseNotreturn> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(AutoMaterialAcctPurchaseNotreturn::getId);
        // 调用Mapper的delete方法执行删除操作
        baseMapper.delete(queryWrapper);

        LambdaQueryWrapper<PurchaseNotreturnOc> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.isNotNull(PurchaseNotreturnOc::getId);
        purchaseNotreturnOcMapper.delete(lambdaQueryWrapper);


        JSONObject jsonObject = JSONObject.parseObject(str);
        if (jsonObject.getInteger("code") == 200) {
            if (jsonObject.containsKey("data") && jsonObject.getJSONObject("data").containsKey("EO_TABLE")) {
                JSONArray jsonArray = jsonObject.getJSONObject("data").getJSONArray("EO_TABLE");
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject1 = jsonArray.getJSONObject(i);
                    AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn = new AutoMaterialAcctPurchaseNotreturn();
                    autoMaterialAcctPurchaseNotreturn.sapPurchaseNotreturnConversion(jsonObject1);
                    save(autoMaterialAcctPurchaseNotreturn);
                    PurchaseNotreturnOc purchaseNotreturnOc = new PurchaseNotreturnOc();
                    BeanUtils.copyProperties(autoMaterialAcctPurchaseNotreturn, purchaseNotreturnOc);
                    purchaseNotreturnOcMapper.insert(purchaseNotreturnOc);
                }
            }
        }
    }

    @Override
    public String importData(List<ExcelImportPurchaseNotreturn> userList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(userList) || userList.size() == 0)
        {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        int updateNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        List<AutoMaterialAcctInventory> autoMaterialAcctInventoryList = autoMaterialAcctInventoryMapper.selectList(null);
        Map<String,List<AutoMaterialAcctInventory>> materialAcctInventoryMap = autoMaterialAcctInventoryList
                .stream().collect(Collectors
                        .groupingBy(autoMaterialAcctInventory -> autoMaterialAcctInventory.getFactory()+"-"+autoMaterialAcctInventory.getMaterial()));
        
        for(ExcelImportPurchaseNotreturn excelImportPurchaseNotreturn : userList){
            try {
                excelImportPurchaseNotreturn.setFactoryCode(excelImportPurchaseNotreturn.getFactoryCode());
                String key = excelImportPurchaseNotreturn.getFactoryCode()+"-"+excelImportPurchaseNotreturn.getMaterial();
                
                if(materialAcctInventoryMap.containsKey(key)){
                    AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn = new AutoMaterialAcctPurchaseNotreturn();
                    BeanUtils.copyProperties(excelImportPurchaseNotreturn,autoMaterialAcctPurchaseNotreturn);
                    autoMaterialAcctPurchaseNotreturn.setMaterialCode(materialAcctInventoryMap.get(key).get(0).getId().longValue()+"");
                    autoMaterialAcctPurchaseNotreturn.setFactoryCode(excelImportPurchaseNotreturn.getFactoryCode());
                    
                    // 根据行号判断新增还是更新
                    if (excelImportPurchaseNotreturn.getId() != null && excelImportPurchaseNotreturn.getId() > 0) {
                        // 更新操作
                        if (isUpdateSupport) {
                            AutoMaterialAcctPurchaseNotreturn existingRecord = getById(excelImportPurchaseNotreturn.getId());
                            if (existingRecord != null) {
                                autoMaterialAcctPurchaseNotreturn.setId(excelImportPurchaseNotreturn.getId());
                                autoMaterialAcctPurchaseNotreturn.setPlanArrivalTime(autoMaterialAcctPurchaseNotreturn.getPlanArrivalTime());
                                updateById(autoMaterialAcctPurchaseNotreturn);
                                updateNum++;
                            } else {
                                failureNum++;
                                failureMsg.append("<br/>行号 " + excelImportPurchaseNotreturn.getId() + " 对应的记录不存在");
                            }
                        } else {
                            failureNum++;
                            failureMsg.append("<br/>行号 " + excelImportPurchaseNotreturn.getId() + " 的数据已存在且不允许更新");
                        }
                    } else {
                        // 新增操作
                        autoMaterialAcctPurchaseNotreturn.setPlanArrivalTime(autoMaterialAcctPurchaseNotreturn.getPlanArrivalTime());
                        save(autoMaterialAcctPurchaseNotreturn);
                        successNum++;
                    }
                } else {
                    failureNum++;
                    failureMsg.append("<br/>物料编码不存在: " + excelImportPurchaseNotreturn.getMaterial());
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>第 " + (userList.indexOf(excelImportPurchaseNotreturn) + 1) + " 行数据导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        
        if (updateNum > 0) {
            successMsg.append("，更新 " + updateNum + " 条");
        }
        
        return successMsg.toString();
    }

    @Transactional
    public Boolean updateAssociationRelationship(AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn){
//        autoMaterialAcctPurchaseNotreturn.getPlanArrivalTimeString();

        autoMaterialAcctPurchaseNotreturn.setPlanArrivalTime(autoMaterialAcctPurchaseNotreturn.getPlanArrivalTime());
        save(autoMaterialAcctPurchaseNotreturn);

//
//        LambdaQueryWrapper<AutoMaterialAcctPurchaseNotreturn> lambdaQueryWrapper2 = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper2.eq(AutoMaterialAcctPurchaseNotreturn::getMaterialCode,autoMaterialAcctPurchaseNotreturn.getMaterialCode()).eq(AutoMaterialAcctPurchaseNotreturn::getPlanDate,autoMaterialAcctPurchaseNotreturn.getPlanDate());
//
//        AutoMaterialAcctPurchaseNotreturn temp = getOne(lambdaQueryWrapper2);
//        if (temp == null){
//            save(autoMaterialAcctPurchaseNotreturn);
//        }else {
//            autoMaterialAcctPurchaseNotreturn.setId(temp.getId());
//            updateById(autoMaterialAcctPurchaseNotreturn);
//        }
//        calculateInventory(autoMaterialAcctPurchaseNotreturn);
        return true;
    }

    /**
     * 查询采购未回
     * 
     * @param id 采购未回主键
     * @return 采购未回
     */
    @Override
    public AutoMaterialAcctPurchaseNotreturn selectAutoMaterialAcctPurchaseNotreturnById(Long id)
    {
        return autoMaterialAcctPurchaseNotreturnMapper.selectAutoMaterialAcctPurchaseNotreturnById(id);
    }

    /**
     * 查询采购未回列表
     * 
     * @param autoMaterialAcctPurchaseNotreturn 采购未回
     * @return 采购未回
     */
    @Override
    public List<AutoMaterialAcctPurchaseNotreturn> selectAutoMaterialAcctPurchaseNotreturnList(AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn)
    {
        return autoMaterialAcctPurchaseNotreturnMapper.selectAutoMaterialAcctPurchaseNotreturnList(autoMaterialAcctPurchaseNotreturn);
    }

    /**
     * 新增采购未回
     * 
     * @param autoMaterialAcctPurchaseNotreturn 采购未回
     * @return 结果
     */
    @Override
    public int insertAutoMaterialAcctPurchaseNotreturn(AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn)
    {
        return autoMaterialAcctPurchaseNotreturnMapper.insertAutoMaterialAcctPurchaseNotreturn(autoMaterialAcctPurchaseNotreturn);
    }

    /**
     * 修改采购未回
     * 
     * @param autoMaterialAcctPurchaseNotreturn 采购未回
     * @return 结果
     */
    @Override
    public int updateAutoMaterialAcctPurchaseNotreturn(AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn)
    {
        return autoMaterialAcctPurchaseNotreturnMapper.updateAutoMaterialAcctPurchaseNotreturn(autoMaterialAcctPurchaseNotreturn);
    }

    /**
     * 批量删除采购未回
     * 
     * @param ids 需要删除的采购未回主键
     * @return 结果
     */
    @Override
    public int deleteAutoMaterialAcctPurchaseNotreturnByIds(Long[] ids)
    {
        return autoMaterialAcctPurchaseNotreturnMapper.deleteAutoMaterialAcctPurchaseNotreturnByIds(ids);
    }

    /**
     * 删除采购未回信息
     * 
     * @param id 采购未回主键
     * @return 结果
     */
    @Override
    public int deleteAutoMaterialAcctPurchaseNotreturnById(Long id)
    {
        return autoMaterialAcctPurchaseNotreturnMapper.deleteAutoMaterialAcctPurchaseNotreturnById(id);
    }
}
