package com.akesobio.report.autoacct.service.impl;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.autoacct.domain.*;
import com.akesobio.report.autoacct.domain.excle.impot.ExcelImportBomMaterialRequire;
import com.akesobio.report.autoacct.mapper.*;
import com.akesobio.report.autoacct.service.IAutoMaterialAcctBomMaterialRequireService;
import com.akesobio.report.autoacct.service.MaterialMDService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * BOM-物料需求Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-22
 */
@Service
public class AutoMaterialAcctBomMaterialRequireServiceImpl extends ServiceImpl<AutoMaterialAcctBomMaterialRequireMapper, BomMaterialRequire> implements IAutoMaterialAcctBomMaterialRequireService
{
    @Resource
    private AutoMaterialAcctBomMaterialRequireMapper autoMaterialAcctBomMaterialRequireMapper;

    @Resource
    private AutoMaterialAcctInventoryMapper autoMaterialAcctInventoryMapper;

    @Resource
    private AutoMaterialAcctMpmInfoMapper autoMaterialAcctMpmInfoMapper;
    @Resource
    private MaterialMDMapper materialMDMapper;


    public String importData(List<ExcelImportBomMaterialRequire> userList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(userList) || userList.size() == 0)
        {
            throw new ServiceException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        int updateNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        Map<String,MaterialMD> materialMDMap = materialMDMapper.getAllMaterials().stream().collect(Collectors.toMap(MaterialMD::getMatnr, Function.identity()));
        LambdaQueryWrapper<MpmInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<MpmInfo> list = autoMaterialAcctMpmInfoMapper.selectList(lambdaQueryWrapper);
        Map<String, MpmInfo> stringAutoMaterialAcctMpmInfoMap = list.stream().collect(Collectors.toMap(MpmInfo::getMpmCode, Function.identity()));

        for (ExcelImportBomMaterialRequire excelImportBomMaterialRequire : userList)
        {
            String key = excelImportBomMaterialRequire.getMaterial();
            if(materialMDMap.containsKey(key) && stringAutoMaterialAcctMpmInfoMap.containsKey(excelImportBomMaterialRequire.getMpmCode())){
                BomMaterialRequire bomMaterialRequire = new BomMaterialRequire();
                BeanUtils.copyProperties(excelImportBomMaterialRequire, bomMaterialRequire);
                
                // 根据ID判断是新增还是更新
                if (excelImportBomMaterialRequire.getId() != null && excelImportBomMaterialRequire.getId() > 0) {
                    // 更新操作
                    if (isUpdateSupport) {
                        BomMaterialRequire existingRecord = getById(excelImportBomMaterialRequire.getId());
                        if (existingRecord != null) {
                            bomMaterialRequire.setId(excelImportBomMaterialRequire.getId());
                            updateById(bomMaterialRequire);
                            updateNum++;
                        } else {
                            failureNum++;
                            String msg = "<br/>" + failureNum + "、行号：" + excelImportBomMaterialRequire.getId() + " 对应的记录不存在";
                            failureMsg.append(msg);
                            continue;
                        }
                    } else {
                        failureNum++;
                        String msg = "<br/>" + failureNum + "、行号：" + excelImportBomMaterialRequire.getId() + " 已存在，但未开启更新模式";
                        failureMsg.append(msg);
                        continue;
                    }
                } else {
                    // 新增操作
                    save(bomMaterialRequire);
                    successNum++;
                }
            }
            else {//物料编码不存在
                failureNum++;
                String msg = "";
                if(!stringAutoMaterialAcctMpmInfoMap.containsKey(excelImportBomMaterialRequire.getMpmCode()))
                     msg += "<br/>" + failureNum + "、mpm代码： " + excelImportBomMaterialRequire.getMpmCode() + "不存在";
                if(!materialMDMap.containsKey(key))
                    msg += "<br/>" + failureNum + "、物料编码不存在： " + key + "不存在";
                failureMsg.append(msg);
            }
        }
        
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + (successNum + updateNum) + " 条，其中新增 " + successNum + " 条，更新 " + updateNum + " 条");
        }
        return successMsg.toString();
    }







    /**
     * 查询BOM-物料需求
     * 
     * @param id BOM-物料需求主键
     * @return BOM-物料需求
     */
    @Override
    public BomMaterialRequire selectAutoMaterialAcctBomMaterialRequireById(Long id)
    {
        return autoMaterialAcctBomMaterialRequireMapper.selectAutoMaterialAcctBomMaterialRequireById(id);
    }

    /**
     * 查询BOM-物料需求列表
     * 
     * @param bomMaterialRequire BOM-物料需求
     * @return BOM-物料需求
     */
    @Override
    public List<BomMaterialRequire> selectAutoMaterialAcctBomMaterialRequireList(BomMaterialRequire bomMaterialRequire)
    {
        return autoMaterialAcctBomMaterialRequireMapper.selectAutoMaterialAcctBomMaterialRequireList(bomMaterialRequire);
    }

    /**
     * 新增BOM-物料需求
     * 
     * @param bomMaterialRequire BOM-物料需求
     * @return 结果
     */
    @Override
    public int insertAutoMaterialAcctBomMaterialRequire(BomMaterialRequire bomMaterialRequire)
    {
        return autoMaterialAcctBomMaterialRequireMapper.insertAutoMaterialAcctBomMaterialRequire(bomMaterialRequire);
    }

    /**
     * 修改BOM-物料需求
     * 
     * @param bomMaterialRequire BOM-物料需求
     * @return 结果
     */
    @Override
    public int updateAutoMaterialAcctBomMaterialRequire(BomMaterialRequire bomMaterialRequire)
    {
        return autoMaterialAcctBomMaterialRequireMapper.updateAutoMaterialAcctBomMaterialRequire(bomMaterialRequire);
    }

    /**
     * 批量删除BOM-物料需求
     * 
     * @param ids 需要删除的BOM-物料需求主键
     * @return 结果
     */
    @Override
    public int deleteAutoMaterialAcctBomMaterialRequireByIds(Long[] ids)
    {
        return autoMaterialAcctBomMaterialRequireMapper.deleteAutoMaterialAcctBomMaterialRequireByIds(ids);
    }

    /**
     * 删除BOM-物料需求信息
     * 
     * @param id BOM-物料需求主键
     * @return 结果
     */
    @Override
    public int deleteAutoMaterialAcctBomMaterialRequireById(Long id)
    {
        return autoMaterialAcctBomMaterialRequireMapper.deleteAutoMaterialAcctBomMaterialRequireById(id);
    }
}
