package com.akesobio.report.sfe.mapper;

import java.util.List;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.sfe.domain.SfeYdf;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 依达方日销售Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@DataSource(value= DataSourceType.SFE)
public interface SfeYdfMapper extends BaseMapper<SfeYdf>
{
    /**
     * 查询依达方日销售
     * 
     * @param id 依达方日销售主键
     * @return 依达方日销售
     */
    public SfeYdf selectSfeYdfById(Long id);

    /**
     * 查询依达方日销售列表
     * 
     * @param sfeYdf 依达方日销售
     * @return 依达方日销售集合
     */
    public List<SfeYdf> selectSfeYdfList(SfeYdf sfeYdf);

    /**
     * 新增依达方日销售
     * 
     * @param sfeYdf 依达方日销售
     * @return 结果
     */
    public int insertSfeYdf(SfeYdf sfeYdf);

    /**
     * 修改依达方日销售
     * 
     * @param sfeYdf 依达方日销售
     * @return 结果
     */
    public int updateSfeYdf(SfeYdf sfeYdf);

    /**
     * 删除依达方日销售
     * 
     * @param id 依达方日销售主键
     * @return 结果
     */
    public int deleteSfeYdfById(Long id);

    /**
     * 批量删除依达方日销售
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSfeYdfByIds(Long[] ids);
}
