package com.akesobio.report.sfe.mapper;

import java.util.List;

import com.akesobio.common.annotation.DataSource;
import com.akesobio.common.enums.DataSourceType;
import com.akesobio.report.sfe.domain.SfeKtn;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 开坦尼日销售Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@DataSource(value= DataSourceType.SFE)
public interface SfeKtnMapper extends BaseMapper<SfeKtn>
{
    /**
     * 查询开坦尼日销售
     * 
     * @param id 开坦尼日销售主键
     * @return 开坦尼日销售
     */
    public SfeKtn selectSfeKtnById(Long id);

    /**
     * 查询开坦尼日销售列表
     * 
     * @param sfeKtn 开坦尼日销售
     * @return 开坦尼日销售集合
     */
    public List<SfeKtn> selectSfeKtnList(SfeKtn sfeKtn);

    /**
     * 新增开坦尼日销售
     * 
     * @param sfeKtn 开坦尼日销售
     * @return 结果
     */
    public int insertSfeKtn(SfeKtn sfeKtn);

    /**
     * 修改开坦尼日销售
     * 
     * @param sfeKtn 开坦尼日销售
     * @return 结果
     */
    public int updateSfeKtn(SfeKtn sfeKtn);

    /**
     * 删除开坦尼日销售
     * 
     * @param id 开坦尼日销售主键
     * @return 结果
     */
    public int deleteSfeKtnById(Long id);

    /**
     * 批量删除开坦尼日销售
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSfeKtnByIds(Long[] ids);
}
